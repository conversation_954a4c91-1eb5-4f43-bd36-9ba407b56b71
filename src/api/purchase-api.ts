import { firebaseFunctions } from "@/root-context";
import { httpsCallable } from "firebase/functions";
import { clearOrdersCache } from "@/api/order-api";

// Response interfaces for purchase functions
export interface MakePurchaseAsBuyerResponse {
  success: boolean;
  message: string;
  lockedAmount: number;
  orderAmount: number;
  lockPercentage: number;
}

export interface MakePurchaseAsSellerResponse {
  success: boolean;
  message: string;
  lockedAmount: number;
  orderAmount: number;
  lockPercentage: number;
}

export interface MakeSecondaryMarketPurchaseResponse {
  success: boolean;
  message: string;
  orderId: string;
  secondaryMarketPrice: number;
  newBuyerId: string;
  oldBuyerId: string;
  netAmountToOldBuyer: number;
  feeAmount: number;
  lockedAmount: number;
}

/**
 * Make a purchase as a buyer (for marketplace orders where seller is looking for buyer)
 * This replaces the old makePurchase function
 */
export const makePurchaseAsBuyer = async (
  orderId: string
): Promise<MakePurchaseAsBuyerResponse> => {
  try {
    const makePurchaseAsBuyerFunction = httpsCallable<
      { buyerId: string; orderId: string },
      MakePurchaseAsBuyerResponse
    >(firebaseFunctions, "makePurchaseAsBuyer");

    // Get current user ID from Firebase Auth
    const auth = await import("firebase/auth");
    const currentUser = auth.getAuth().currentUser;
    
    if (!currentUser) {
      throw new Error("User must be authenticated to make a purchase");
    }

    const result = await makePurchaseAsBuyerFunction({
      buyerId: currentUser.uid,
      orderId,
    });

    // Clear cache after purchase
    clearOrdersCache();

    return result.data;
  } catch (error) {
    console.error("Error making purchase as buyer:", error);
    throw error;
  }
};

/**
 * Make a purchase as a seller (for marketplace orders where buyer is looking for seller)
 * This replaces the old completePurchase function
 */
export const makePurchaseAsSeller = async (
  orderId: string
): Promise<MakePurchaseAsSellerResponse> => {
  try {
    const makePurchaseAsSellerFunction = httpsCallable<
      { sellerId: string; orderId: string },
      MakePurchaseAsSellerResponse
    >(firebaseFunctions, "makePurchaseAsSeller");

    // Get current user ID from Firebase Auth
    const auth = await import("firebase/auth");
    const currentUser = auth.getAuth().currentUser;
    
    if (!currentUser) {
      throw new Error("User must be authenticated to make a purchase");
    }

    const result = await makePurchaseAsSellerFunction({
      sellerId: currentUser.uid,
      orderId,
    });

    // Clear cache after purchase
    clearOrdersCache();

    return result.data;
  } catch (error) {
    console.error("Error making purchase as seller:", error);
    throw error;
  }
};

/**
 * Purchase an order from secondary market
 * Moved from order-api.ts
 */
export const makeSecondaryMarketPurchase = async (
  orderId: string
): Promise<MakeSecondaryMarketPurchaseResponse> => {
  try {
    const makeSecondaryMarketPurchaseFunction = httpsCallable<
      { orderId: string },
      MakeSecondaryMarketPurchaseResponse
    >(firebaseFunctions, "makeSecondaryMarketPurchase");

    const result = await makeSecondaryMarketPurchaseFunction({ orderId });

    // Clear cache after secondary market purchase
    clearOrdersCache();

    return result.data;
  } catch (error) {
    console.error("Error making secondary market purchase:", error);
    throw error;
  }
};
