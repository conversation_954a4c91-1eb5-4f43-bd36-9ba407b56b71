import { doc, getDoc, setDoc } from 'firebase/firestore';
import { firestore } from '@/root-context';

export interface AppConfig {
  id: string;
  minDepositAmount: number; // in TON
  depositFee: number; // in TON
  withdrawalFee: number; // in TON
  maxDepositAmount?: number; // in TON
  maintenanceMode?: boolean;
  sellerLockPercentage?: number; // Percentage of order amount locked by seller (0.0-1.0)
  buyerLockPercentage?: number; // Percentage of order amount locked by buyer (0.0-1.0)
  minSecondaryMarketPrice?: number; // Minimum price for secondary market resale in TON
  updatedAt?: Date;
}

/**
 * Simple cache for app config
 *
 * Features:
 * - Caches app config for 5 minutes to reduce Firebase reads
 * - Automatically invalidates cache on update operations
 * - Provides getCachedAppConfig() for cached access
 * - Use clearAppConfigCache() to manually clear cache if needed
 */
let cachedAppConfig: AppConfig | null = null;
let cacheTimestamp = 0;
const CACHE_DURATION = 24 * 60 * 60 * 1000; // 1 day in milliseconds

// Cache management functions
const isCacheValid = () => {
  return Date.now() - cacheTimestamp < CACHE_DURATION;
};

const clearCache = () => {
  cachedAppConfig = null;
  cacheTimestamp = 0;
};

const updateCache = (config: AppConfig) => {
  cachedAppConfig = config;
  cacheTimestamp = Date.now();
};

const getCachedAppConfig = (): AppConfig | null => {
  if (!isCacheValid()) {
    clearCache();
    return null;
  }
  return cachedAppConfig;
};

/**
 * Get app configuration from Firebase with caching
 */
export const getAppConfig = async (): Promise<AppConfig | null> => {
  // Try to return from cache first
  const cachedConfig = getCachedAppConfig();
  if (cachedConfig) {
    return cachedConfig;
  }

  try {
    const configRef = doc(firestore, 'app_config', 'main');
    const configDoc = await getDoc(configRef);

    let config: AppConfig;

    if (!configDoc.exists()) {
      console.warn('App config document not found, using defaults');
      // Return default config if not found
      config = {
        id: 'main',
        minDepositAmount: 1, // 1 TON
        depositFee: 0.1, // 0.1 TON
        withdrawalFee: 0.1, // 0.1 TON
        maxDepositAmount: 1000, // 1000 TON
        maintenanceMode: false,
        sellerLockPercentage: 0.2, // 20% for sellers
        buyerLockPercentage: 1.0, // 100% for buyers
      };
    } else {
      config = {
        id: configDoc.id,
        ...configDoc.data()
      } as AppConfig;
    }

    // Update cache with fetched config
    updateCache(config);

    return config;
  } catch (error) {
    console.error('Error getting app config:', error);
    throw error;
  }
};

/**
 * Get minimum deposit amount from config
 */
export const getMinDepositAmount = async (): Promise<number> => {
  try {
    const config = await getAppConfig();
    return config?.minDepositAmount || 1;
  } catch (error) {
    console.error('Error getting min deposit amount:', error);
    return 1; // Default fallback
  }
};

/**
 * Get deposit fee from config
 */
export const getDepositFee = async (): Promise<number> => {
  try {
    const config = await getAppConfig();
    return config?.depositFee || 0.1;
  } catch (error) {
    console.error('Error getting deposit fee:', error);
    return 0.1; // Default fallback
  }
};

/**
 * Update app configuration in Firebase and clear cache
 */
export const updateAppConfig = async (updates: Partial<AppConfig>): Promise<void> => {
  try {
    const configRef = doc(firestore, 'app_config', 'main');

    const updateData = {
      ...updates,
      updatedAt: new Date()
    };

    await setDoc(configRef, updateData, { merge: true });

    // Clear cache after updating
    clearCache();

    console.log('App config updated successfully:', updateData);
  } catch (error) {
    console.error('Error updating app config:', error);
    throw error;
  }
};

/**
 * Setup initial app configuration in Firebase
 */
export const setupAppConfig = async (): Promise<AppConfig> => {
  try {
    const configRef = doc(firestore, 'app_config', 'main');

    const defaultConfig: AppConfig = {
      id: 'main',
      minDepositAmount: 1, // 1 TON minimum deposit
      depositFee: 0.1, // 0.1 TON deposit fee
      withdrawalFee: 0.1, // 0.1 TON withdrawal fee
      maxDepositAmount: 1000, // 1000 TON maximum deposit
      maintenanceMode: false,
      sellerLockPercentage: 0.2, // 20% for sellers
      buyerLockPercentage: 1.0, // 100% for buyers
      updatedAt: new Date(),
    };

    await setDoc(configRef, defaultConfig);

    // Clear cache after setup
    clearCache();

    console.log('App config created successfully:', defaultConfig);
    return defaultConfig;
  } catch (error) {
    console.error('Error setting up app config:', error);
    throw error;
  }
};

/**
 * Public function to manually clear the app config cache
 */
export const clearAppConfigCache = () => {
  clearCache();
};
