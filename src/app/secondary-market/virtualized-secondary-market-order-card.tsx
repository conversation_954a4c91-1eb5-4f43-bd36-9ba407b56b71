"use client";

import React from "react";
import { SecondaryMarketOrderCard } from "./secondary-market-order-card";
import { GridItem } from "@/components/ui/virtualized-grid";
import { Collection, OrderEntity } from "@/core.constants";

interface VirtualizedSecondaryMarketOrderCardProps {
  order: OrderEntity;
  collection: Collection | undefined;
  onClick: () => void;
  animated?: boolean;
  index: number;
  initialRenderedCount?: number;
}

export const VirtualizedSecondaryMarketOrderCard: React.FC<VirtualizedSecondaryMarketOrderCardProps> = ({
  order,
  collection,
  onClick,
  animated,
  index,
  initialRenderedCount = 15,
}) => {
  const itemId = `secondary-order-${order.id}`;

  return (
    <GridItem
      itemId={itemId}
      index={index}
      initialRenderedCount={initialRenderedCount}
    >
      <SecondaryMarketOrderCard
        animated={animated}
        order={order}
        collection={collection}
        onClick={onClick}
      />
    </GridItem>
  );
};
