"use client";
import "@telegram-apps/telegram-ui/dist/styles.css";

import { Button } from "@/components/ui/button";
import { CollectionSelect } from "@/components/ui/collection-select";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { firebaseFunctions, useRootContext } from "@/root-context";
import { httpsCallable } from "firebase/functions";
import { AlertTriangle, ShoppingCart } from "lucide-react";
import { useState } from "react";
import { toast } from "sonner";
import { Drawer } from "vaul";
import { TonLogo } from "@/components/TonLogo";
import { Caption } from "@telegram-apps/telegram-ui";

interface CreateOrderDrawerProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  orderType: "seller" | "buyer";
  onOrderCreated?: () => void;
}

export function CreateOrderDrawer({
  open,
  onOpenChange,
  orderType,
  onOrderCreated,
}: CreateOrderDrawerProps) {
  const { currentUser, collections, appConfig } = useRootContext();
  const [selectedCollection, setSelectedCollection] = useState("");
  const [itemPrice, setItemPrice] = useState("");
  const [loading, setLoading] = useState(false);

  const selectedCollectionData = collections.find(
    (c) => c.id === selectedCollection
  );
  const price = parseFloat(itemPrice);
  const isValidPrice =
    !isNaN(price) &&
    price > 0 &&
    selectedCollectionData &&
    price >= selectedCollectionData.floorPrice;

  const lockPercentage =
    orderType === "seller"
      ? appConfig?.sellerLockPercentage || 0.2
      : appConfig?.buyerLockPercentage || 1.0;

  const lockAmount = isValidPrice ? price * lockPercentage : 0;
  const availableBalance = currentUser?.balance
    ? currentUser.balance.sum - currentUser.balance.locked
    : 0;
  const hasSufficientBalance = lockAmount <= availableBalance;

  const handleCreateOrder = async () => {
    if (!isValidPrice || !selectedCollectionData || !currentUser) {
      toast.error("Please fill in all required fields");
      return;
    }

    if (!hasSufficientBalance) {
      toast.error("Insufficient available balance");
      return;
    }

    try {
      setLoading(true);

      const functionName =
        orderType === "seller" ? "createOrderAsSeller" : "createOrderAsBuyer";
      const createOrderFunction = httpsCallable(
        firebaseFunctions,
        functionName
      );

      const result = await createOrderFunction({
        collectionId: selectedCollection,
        amount: price,
        ...(orderType === "seller"
          ? { sellerId: currentUser.id }
          : { buyerId: currentUser.id }),
      });

      const message =
        result.data &&
        typeof result.data === "object" &&
        "message" in result.data
          ? (result.data as { message: string }).message
          : "Order created successfully!";
      toast.success(message);

      // Reset form
      setSelectedCollection("");
      setItemPrice("");
      onOpenChange(false);

      // Notify parent component
      if (onOrderCreated) {
        onOrderCreated();
      }
    } catch (error: unknown) {
      console.error("Order creation failed:", error);
      const errorMessage =
        error instanceof Error
          ? error.message
          : "Failed to create order. Please try again.";
      toast.error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    setSelectedCollection("");
    setItemPrice("");
    onOpenChange(false);
  };

  console.log("ORDER TYPE:", orderType);

  return (
    <Drawer.Root
      open={open}
      onOpenChange={onOpenChange}
      shouldScaleBackground
      modal={true}
      dismissible={true}
    >
      <Drawer.Portal>
        <Drawer.Title />
        <Drawer.Overlay className="fixed inset-0 bg-black/40 z-[100]" />
        <Drawer.Content
          className="bg-[#17212b] flex flex-col rounded-t-[20px] mt-16 fixed bottom-0 left-0 right-0 z-[101] outline-none focus:outline-none"
          style={
            {
              "--tgui--bg_color": "var(--tg-theme-bg-color)",
              "--tgui--secondary_bg_color":
                "var(--tg-theme-secondary-bg-color)",
              "--tgui--text_color": "var(--tg-theme-text-color)",
              "--tgui--hint_color": "var(--tg-theme-hint-color)",
              "--tgui--link_color": "var(--tg-theme-link-color)",
              "--tgui--button_color": "var(--tg-theme-button-color)",
              "--tgui--button_text_color": "var(--tg-theme-button-text-color)",
              "--tgui--secondary_hint_color":
                "var(--tg-theme-subtitle-text-color)",
              "--tgui--outline": "var(--tg-theme-section-separator-color)",
              "--tgui--accent_text_color": "var(--tg-theme-accent-text-color)",
              "--tgui--section_bg_color": "var(--tg-theme-section-bg-color)",
              "--tgui--header_bg_color": "var(--tg-theme-header-bg-color)",
              "--tgui--destructive_text_color":
                "var(--tg-theme-destructive-text-color)",
              "--ring": "var(--tg-theme-accent-text-color)",
            } as React.CSSProperties
          }
        >
          <div className="p-6 bg-[#17212b] rounded-t-[20px] flex-1 max-h-[85vh] overflow-y-auto">
            <div className="mx-auto w-12 h-1.5 flex-shrink-0 rounded-full bg-[#708499] mb-6 cursor-grab active:cursor-grabbing touch-manipulation" />

            <div className="max-w-md mx-auto space-y-6">
              {/* Header with Icon */}
              <div className="flex items-center gap-3 mb-6">
                <div className="w-10 h-10 bg-[#6ab2f2] rounded-full flex items-center justify-center">
                  <ShoppingCart className="w-5 h-5 text-white" />
                </div>
                <div>
                  <h2 className="text-xl font-semibold text-[#f5f5f5]">
                    Create {orderType === "seller" ? "Sell" : "Buy"} Order
                  </h2>
                  <Caption level="2" weight="3" className="text-[#708499]">
                    {orderType === "seller"
                      ? "List your item for sale"
                      : "Place a buy order for an item"}
                  </Caption>
                </div>
              </div>

              {!appConfig ? (
                <div className="text-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#6ab2f2] mx-auto"></div>
                  <Caption level="2" weight="3" className="text-[#708499] mt-2">
                    Loading configuration...
                  </Caption>
                </div>
              ) : (
                <>
                  {/* Information Card */}
                  <div className="bg-[#232e3c]/50 rounded-2xl p-4 border border-[#3a4a5c]/30">
                    <div className="w-full flex items-start gap-3">
                      <AlertTriangle className="w-5 h-5 text-[#6ab2f2] flex-shrink-0 mt-0.5" />
                      <div className="w-full text-sm">
                        <p className="font-medium text-[#f5f5f5] mb-2">
                          Order Information
                        </p>
                        <div className="space-y-2">
                          <div className="flex justify-between items-center py-1">
                            <span className="text-[#708499]">
                              Lock percentage:
                            </span>
                            <span className="text-[#6ab2f2] font-semibold">
                              {(lockPercentage * 100).toFixed(0)}%
                            </span>
                          </div>
                          <div className="flex justify-between items-center py-1">
                            <span className="text-[#708499]">
                              Available balance:
                            </span>
                            <div className="flex items-center gap-1">
                              <span className="text-[#6ab2f2] font-semibold">
                                {availableBalance.toFixed(2)}
                              </span>
                              <TonLogo size={14} />
                            </div>
                          </div>
                          <div className="text-[#708499] text-xs">
                            Price must be at least the collection floor price
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-4">
                    {/* Collection Selection */}
                    <div>
                      <CollectionSelect
                        animated
                        collections={collections}
                        value={selectedCollection}
                        onValueChange={setSelectedCollection}
                        placeholder="Select a collection"
                        className="mt-2"
                      />
                    </div>

                    {/* Item Price */}
                    <div>
                      <Label
                        htmlFor="item-price"
                        className="text-sm font-medium text-[#f5f5f5]"
                      >
                        Item Price (TON)
                      </Label>
                      <Input
                        id="item-price"
                        type="number"
                        step="0.01"
                        placeholder={
                          selectedCollectionData
                            ? `Min ${selectedCollectionData.floorPrice} TON`
                            : "Enter price"
                        }
                        value={itemPrice}
                        onChange={(e) => setItemPrice(e.target.value)}
                        className="mt-2 bg-[#232e3c]/50 border-[#3a4a5c]/50 text-[#f5f5f5] placeholder:text-[#708499] focus:border-[#6ab2f2] focus:ring-[#6ab2f2]/20"
                        min={selectedCollectionData?.floorPrice || 0}
                      />
                      {selectedCollectionData &&
                        itemPrice &&
                        price < selectedCollectionData.floorPrice && (
                          <Caption
                            level="2"
                            weight="3"
                            className="text-[#ec3942] mt-1"
                          >
                            Price must be at least{" "}
                            {selectedCollectionData.floorPrice} TON (floor
                            price)
                          </Caption>
                        )}
                    </div>

                    {/* Lock Amount Summary */}
                    {isValidPrice && (
                      <div className="bg-[#232e3c]/50 rounded-2xl p-4 border border-[#3a4a5c]/30">
                        <div className="space-y-3">
                          <div className="flex justify-between items-center">
                            <span className="text-[#708499]">Item price:</span>
                            <div className="flex items-center gap-1">
                              <span className="text-[#6ab2f2] font-semibold">
                                {price.toFixed(2)}
                              </span>
                              <TonLogo size={14} />
                            </div>
                          </div>
                          <div className="flex justify-between items-center">
                            <span className="text-[#708499]">
                              Lock amount ({(lockPercentage * 100).toFixed(0)}
                              %):
                            </span>
                            <div className="flex items-center gap-1">
                              <span className="text-[#6ab2f2] font-semibold">
                                {lockAmount.toFixed(2)}
                              </span>
                              <TonLogo size={14} />
                            </div>
                          </div>
                          <div className="flex justify-between items-center">
                            <span className="text-[#708499]">
                              Available balance:
                            </span>
                            <div className="flex items-center gap-1">
                              <span className="text-[#6ab2f2] font-semibold">
                                {availableBalance.toFixed(2)}
                              </span>
                              <TonLogo size={14} />
                            </div>
                          </div>
                          <div className="flex justify-between items-center font-medium border-t border-[#3a4a5c]/30 pt-3">
                            <span className="text-[#f5f5f5]">
                              Remaining after lock:
                            </span>
                            <div className="flex items-center gap-1">
                              <span
                                className={`font-semibold ${
                                  hasSufficientBalance
                                    ? "text-[#6ab2f2]"
                                    : "text-[#ec3942]"
                                }`}
                              >
                                {(availableBalance - lockAmount).toFixed(2)}
                              </span>
                              <TonLogo size={14} />
                            </div>
                          </div>
                        </div>
                      </div>
                    )}

                    {/* Action Buttons */}
                    <div className="space-y-3 pt-4">
                      <Button
                        onClick={handleCreateOrder}
                        disabled={
                          !isValidPrice || !hasSufficientBalance || loading
                        }
                        className="w-full h-12 bg-[#6ab2f2] hover:bg-[#6ab2f2]/90 text-white border-0 rounded-2xl"
                      >
                        {loading ? (
                          "Creating..."
                        ) : (
                          <>
                            Create
                            {isValidPrice && (
                              <>
                                {" "}
                                &#40;{price.toFixed(2)}{" "}
                                <TonLogo className="-m-2" size={18} />
                                <span className="-ml-1">&#41;</span>
                              </>
                            )}
                          </>
                        )}
                      </Button>

                      <Button
                        variant="outline"
                        onClick={handleClose}
                        className="w-full h-12 bg-transparent border-[#3a4a5c]/50 text-[#708499] hover:bg-[#232e3c]/50 hover:text-[#f5f5f5] rounded-2xl"
                        disabled={loading}
                      >
                        Cancel
                      </Button>
                    </div>

                    {!hasSufficientBalance && isValidPrice && (
                      <div className="text-center mt-4">
                        <Caption
                          level="2"
                          weight="3"
                          className="text-[#ec3942]"
                        >
                          Insufficient balance to lock {lockAmount.toFixed(2)}{" "}
                          TON
                        </Caption>
                      </div>
                    )}
                  </div>
                </>
              )}
            </div>
          </div>
        </Drawer.Content>
      </Drawer.Portal>
    </Drawer.Root>
  );
}
