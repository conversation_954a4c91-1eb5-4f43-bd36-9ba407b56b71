"use client";

import { useImagePath } from "@/hooks/useImagePath";
import Image from "next/image";
import TgsViewer from "./TgsViewer";

interface TgsOrImageProps {
  isImage: boolean;
  collectionId: string;
  imageProps?: {
    alt: string;
    fill?: boolean;
    className?: string;
    loading?: "lazy" | "eager";
    sizes?: string;
    onError?: (e: React.SyntheticEvent<HTMLImageElement, Event>) => void;
  };
  tgsProps?: {
    style?: React.CSSProperties;
    className?: string;
  };
}

export function TgsOrImage({
  isImage,
  collectionId,
  imageProps,
  tgsProps,
}: TgsOrImageProps) {
  const imagePath = useImagePath({ collectionId, format: "png" });

  const tgsPath = useImagePath({ collectionId, format: "tgs" });

  // Don't render until we have a valid collectionId
  if (!collectionId) {
    return null;
  }

  if (isImage && imageProps) {
    // Don't render Image until we have a src
    if (!imagePath.src) {
      return null;
    }

    return (
      <Image
        src={imagePath.src}
        alt={imageProps.alt}
        fill={imageProps.fill}
        className={imageProps.className}
        loading={imageProps.loading}
        sizes={imageProps.sizes}
        onError={(e) => {
          imagePath.onError();
          imageProps.onError?.(e);
        }}
      />
    );
  }

  if (!isImage && tgsProps) {
    // For TGS files, use local path directly since CDN might not have them

    return (
      <TgsViewer
        tgsUrl={tgsPath.src}
        style={tgsProps.style}
        onError={tgsPath.onError}
        className={tgsProps.className}
      />
    );
  }

  return null;
}
