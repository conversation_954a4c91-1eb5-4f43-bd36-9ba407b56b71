"use client";

import { useState, useCallback } from 'react';
import { Drawer } from 'vaul';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useTonConnectUI } from '@tonconnect/ui-react';
import { toNano } from '@ton/core';
import { toast } from 'sonner';
import { AlertTriangle, ArrowUp } from 'lucide-react';
import { useRootContext } from '@/root-context';

import { CountdownPopup } from '@/components/CountdownPopup';
import { TonLogo } from '@/components/TonLogo';
import { Caption } from '@telegram-apps/telegram-ui';

interface DepositDrawerProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function DepositDrawer({ open, onOpenChange }: DepositDrawerProps) {
  const [tonConnectUI] = useTonConnectUI();
  const { appConfig, refetchUser } = useRootContext();
  const [depositAmount, setDepositAmount] = useState('');
  const [loading, setLoading] = useState(false);
  const [showCountdownPopup, setShowCountdownPopup] = useState(false);

  const refetchUserAndClosePopup = useCallback(async () => {
    try {
      await refetchUser();
      toast.success('Balance updated successfully!');
    } catch (error) {
      console.error('Failed to refetch user:', error);
      toast.error('Failed to update balance');
    } finally {
      setShowCountdownPopup(false);
    }
  }, [refetchUser]);

  const handleClosePopup = () => {
    setShowCountdownPopup(false);
  };

  const validateAmount = (amount: string): boolean => {
    if (!amount || !appConfig) return false;
    
    const numAmount = parseFloat(amount);
    if (isNaN(numAmount) || numAmount <= 0) return false;
    
    return numAmount >= appConfig.minDepositAmount;
  };

  const isValidAmount = validateAmount(depositAmount);
  const marketplaceWallet = process.env.NEXT_PUBLIC_MARKETPLACE_WALLET_ADDRESS;

  const handleDeposit = async () => {
    if (!isValidAmount || !appConfig || !marketplaceWallet) {
      toast.error('Invalid deposit amount or configuration');
      return;
    }

    if (!tonConnectUI.account?.address) {
      toast.error('Please connect your wallet first');
      return;
    }

    try {
      setLoading(true);
      
      const amount = parseFloat(depositAmount);
      const totalAmount = amount + appConfig.depositFee;
      
      // Create transaction
      const transaction = {
        validUntil: Math.floor(Date.now() / 1000) + 300, // 5 minutes
        messages: [
          {
            address: marketplaceWallet,
            amount: toNano(totalAmount.toString()).toString(),
            payload: '', // Optional: add memo/comment
          },
        ],
      };

      console.log('Sending transaction:', transaction);
      
      // Send transaction
      const result = await tonConnectUI.sendTransaction(transaction);
      
      console.log('Transaction result:', result);
      toast.success(`Deposit of ${amount} TON initiated successfully!`);

      // Reset form and close drawer
      setDepositAmount('');
      onOpenChange(false);

      // Show countdown popup
      setShowCountdownPopup(true);
      
    } catch (error) {
      console.error('Deposit failed:', error);
      toast.error('Deposit failed. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    setDepositAmount('');
    onOpenChange(false);
  };

  return (
    <Drawer.Root
      open={open}
      onOpenChange={onOpenChange}
      shouldScaleBackground
      modal={true}
      dismissible={true}
    >
      <Drawer.Portal>
      <Drawer.Title/>
        <Drawer.Overlay className="fixed inset-0 bg-black/40 z-[100]" />
        <Drawer.Content className="bg-[#17212b] flex flex-col rounded-t-[20px] mt-16 fixed bottom-0 left-0 right-0 z-[101] outline-none focus:outline-none">
          <div className="p-6 bg-[#17212b] rounded-t-[20px] flex-1 max-h-[85vh] overflow-y-auto">
            <div className="mx-auto w-12 h-1.5 flex-shrink-0 rounded-full bg-[#708499] mb-6 cursor-grab active:cursor-grabbing touch-manipulation" />

            <div className="max-w-md mx-auto space-y-6">
              {/* Header with Icon */}
              <div className="flex items-center gap-3 mb-6">
                <div className="w-10 h-10 bg-[#6ab2f2] rounded-full flex items-center justify-center">
                  <ArrowUp className="w-5 h-5 text-white" />
                </div>
                <div>
                  <h2 className="text-xl font-semibold text-[#f5f5f5]">Deposit Funds</h2>
                  <Caption level="2" weight="3" className="text-[#708499]">
                    Add TON to your marketplace balance
                  </Caption>
                </div>
              </div>

              {!appConfig ? (
                <div className="text-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#6ab2f2] mx-auto"></div>
                  <Caption level="2" weight="3" className="text-[#708499] mt-2">
                    Loading configuration...
                  </Caption>
                </div>
              ) : (
                <>
                  {/* Information Card */}
                  <div className="bg-[#232e3c]/50 rounded-2xl p-4 border border-[#3a4a5c]/30">
                    <div className="w-full flex items-start gap-3">
                      <AlertTriangle className="w-5 h-5 text-[#6ab2f2] flex-shrink-0 mt-0.5" />
                      <div className="w-full text-sm">
                        <p className="w-full font-medium text-[#f5f5f5] mb-2">Deposit Information</p>
                        <div className="space-y-2">
                          <div className="w-f flex justify-between items-center py-1">
                            <span className="text-[#708499]">Minimum deposit:</span>
                            <div className="flex items-center gap-1">
                              <span className="text-[#6ab2f2] font-semibold">
                                {appConfig?.minDepositAmount || 1}
                              </span>
                              <TonLogo size={14} />
                            </div>
                          </div>
                          <div className="flex justify-between items-center py-1">
                            <span className="text-[#708499]">Deposit fee:</span>
                            <div className="flex items-center gap-1">
                              <span className="text-[#6ab2f2] font-semibold">
                                {appConfig?.depositFee || 0.1}
                              </span>
                              <TonLogo size={14} />
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Deposit Amount Input */}
                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="deposit-amount" className="text-sm font-medium text-[#f5f5f5]">
                        Deposit Amount (TON)
                      </Label>
                      <Input
                        id="deposit-amount"
                        type="number"
                        placeholder={`Min ${appConfig?.minDepositAmount || 1} TON`}
                        value={depositAmount}
                        onChange={(e) => setDepositAmount(e.target.value)}
                        className="mt-2 bg-[#232e3c]/50 border-[#3a4a5c]/50 text-[#f5f5f5] placeholder:text-[#708499] focus:border-[#6ab2f2] focus:ring-[#6ab2f2]/20"
                        min={appConfig?.minDepositAmount || 1}
                        step="0.1"
                      />
                      {depositAmount && !isValidAmount && (
                        <Caption level="2" weight="3" className="text-[#ec3942] mt-1">
                          Amount must be at least {appConfig?.minDepositAmount || 1} TON
                        </Caption>
                      )}
                    </div>

                    {/* Amount Summary */}
                    {depositAmount && isValidAmount && (
                      <div className="bg-[#232e3c]/50 rounded-2xl p-4 border border-[#3a4a5c]/30">
                        <div className="space-y-3">
                          <div className="flex justify-between items-center">
                            <span className="text-[#708499]">Deposit amount:</span>
                            <div className="flex items-center gap-1">
                              <span className="text-[#6ab2f2] font-semibold">{depositAmount}</span>
                              <TonLogo size={14} />
                            </div>
                          </div>
                          <div className="flex justify-between items-center">
                            <span className="text-[#708499]">Deposit fee:</span>
                            <div className="flex items-center gap-1">
                              <span className="text-[#6ab2f2] font-semibold">
                                {appConfig?.depositFee || 0.1}
                              </span>
                              <TonLogo size={14} />
                            </div>
                          </div>
                          <div className="flex justify-between items-center font-medium border-t border-[#3a4a5c]/30 pt-3">
                            <span className="text-[#f5f5f5]">Total to pay:</span>
                            <div className="flex items-center gap-1">
                              <span className="text-[#6ab2f2] font-semibold">
                                {(parseFloat(depositAmount) + (appConfig?.depositFee || 0.1)).toFixed(1)}
                              </span>
                              <TonLogo size={14} />
                            </div>
                          </div>
                        </div>
                      </div>
                    )}

                    {/* Action Buttons */}
                    <div className="space-y-3 pt-4">
                      <Button
                        onClick={handleDeposit}
                        disabled={!isValidAmount || loading || !tonConnectUI.account?.address}
                        className="w-full h-12 bg-[#6ab2f2] hover:bg-[#6ab2f2]/90 text-white border-0 rounded-2xl"
                      >
                        {loading ? 'Processing...' : (
                          <>
                            Deposit {depositAmount && isValidAmount ? (
                              <>
                                &#40;{(parseFloat(depositAmount) + (appConfig?.depositFee || 0.1)).toFixed(1)}{" "}
                                <TonLogo className="-m-2" size={18} />
                                <span className="-ml-1 translate-x-[1px]">&#41;</span>
                              </>
                            ) : ''}
                          </>
                        )}
                      </Button>

                      <Button
                        variant="outline"
                        onClick={handleClose}
                        className="w-full h-12 bg-transparent border-[#3a4a5c]/50 text-[#708499] hover:bg-[#232e3c]/50 hover:text-[#f5f5f5] rounded-2xl"
                        disabled={loading}
                      >
                        Cancel
                      </Button>
                    </div>

                    {!tonConnectUI.account?.address && (
                      <div className="text-center mt-4">
                        <Caption level="2" weight="3" className="text-[#ec3942]">
                          Please connect your wallet to make a deposit
                        </Caption>
                      </div>
                    )}
                  </div>
                </>
              )}
            </div>
          </div>
        </Drawer.Content>
      </Drawer.Portal>

      {/* Countdown Popup */}
      <CountdownPopup
        show={showCountdownPopup}
        onClose={handleClosePopup}
        onComplete={refetchUserAndClosePopup}
        initialSeconds={60}
        title="Deposit Processing"
        message="You will receive your funds within"
      />
    </Drawer.Root>
  );
}
