"use client";

import { TrendingUp, ShoppingCart, User, Repeat } from "lucide-react";
import { usePathname } from "next/navigation";
import Link from "next/link";

export default function Footer() {
  const pathname = usePathname();

  const navItems = [
    {
      icon: TrendingUp,
      label: "Marketplace",
      route: "/marketplace",
      active: pathname === "/marketplace",
    },
    {
      icon: Repeat,
      label: "Secondary market",
      route: "/secondary-market",
      active: pathname === "/secondary-market",
    },
    {
      icon: ShoppingCart,
      label: "Orders",
      route: "/orders",
      active: pathname === "/orders",
    },
    {
      icon: User,
      label: "Profile",
      route: "/profile",
      active: pathname === "/profile",
    },
  ];

  return (
    <footer className="fixed bottom-0 left-0 right-0 bg-[#17212b] text-[#f5f5f5] border-t border-[#3a4a5c] z-50 h-16">
      <div className="grid grid-cols-4 items-center h-full">
        {navItems.map((item, index) => {
          const IconComponent = item.icon;
          return (
            <Link
              key={index}
              href={item.route}
              className={`flex flex-col items-center gap-1 py-3 h-auto transition-colors ${
                item.active ? "text-[#6ab2f2]" : "text-[#708499] hover:text-[#f5f5f5]"
              }`}
            >
              <IconComponent className="w-5 h-5" />
              <span className="text-[10px] font-medium">{item.label}</span>
            </Link>
          );
        })}
      </div>
    </footer>
  );
}
